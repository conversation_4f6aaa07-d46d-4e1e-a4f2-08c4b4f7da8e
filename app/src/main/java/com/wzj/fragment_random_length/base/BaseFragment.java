package com.wzj.fragment_random_length.base;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;
import androidx.viewbinding.ViewBinding;

/**
 * BaseFragment - ViewBinding架构的基础Fragment类
 *
 * 功能特性：
 * 1. ViewBinding支持和自动管理
 * 2. Fragment生命周期回调
 * 3. 智能的可见性状态管理
 * 4. 第一次出现的特殊处理
 * 5. 状态保存和恢复
 * 6. 安全的操作执行
 *
 * @param <VB> ViewBinding类型
 */
public abstract class BaseFragment<VB extends ViewBinding> extends Fragment {

    private static final String TAG = "BaseFragment";

    // ==================== ViewBinding组件 ====================

    /** ViewBinding实例 */
    protected VB binding;

    // ==================== 状态标志 ====================

    /** 是否第一次可见 */
    private boolean isFirstTimeVisible = true;

    /** 是否已初始化 */
    private boolean isInitialized = false;

    /** 当前是否可见 */
    private boolean isCurrentlyVisible = false;

    // ==================== Fragment生命周期 ====================

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                            @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView: " + getClass().getSimpleName());

        if (binding == null) {
            try {
                // 初始化ViewBinding
                binding = getViewBinding(inflater, container);

                if (binding != null) {
                    // 初始化视图
                    initView();
                    isInitialized = true;

                    Log.d(TAG, "View created successfully: " + getClass().getSimpleName());
                } else {
                    Log.e(TAG, "Failed to create ViewBinding for: " + getClass().getSimpleName());
                }
            } catch (Exception e) {
                Log.e(TAG, "Error creating view for: " + getClass().getSimpleName(), e);
            }
        }
        return binding != null ? binding.getRoot() : null;
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);
        Log.d(TAG, "onViewCreated: " + getClass().getSimpleName());
    }

    @Override
    public void onResume() {
        super.onResume();
        Log.d(TAG, "onResume: " + getClass().getSimpleName());

        // 调用Fragment恢复回调
        onFragmentResume();

        if(isFirstTimeVisible){
            isFirstTimeVisible = false;
            onFirstTimeVisible();
        }

        // Fragment变为可见
        if (!isCurrentlyVisible) {
            isCurrentlyVisible = true;
            onVisibleRefresh();
        }
    }

    @Override
    public void onPause() {
        super.onPause();
        Log.d(TAG, "onPause: " + getClass().getSimpleName());

        // 调用Fragment暂停回调
        onFragmentPause();

        // Fragment变为不可见
        if (isCurrentlyVisible) {
            isCurrentlyVisible = false;
            onInVisibleRefresh();
        }
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        Log.d(TAG, "onDestroyView: " + getClass().getSimpleName());

        // 重置状态
        isCurrentlyVisible = false;
        isInitialized = false;
        isFirstTimeVisible = true;
        // 清理ViewBinding
        binding = null;
    }

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        Log.d(TAG, "onSaveInstanceState: " + getClass().getSimpleName());

        // 保存基础状态
        outState.putBoolean("isFirstTimeVisible", isFirstTimeVisible);
        outState.putBoolean("isInitialized", isInitialized);

        // 调用子类的状态保存方法
        saveInstanceState(outState);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        Log.d(TAG, "onViewStateRestored: " + getClass().getSimpleName());

        // 如果有保存的状态，恢复基础状态并调用子类的状态恢复方法
        if (savedInstanceState != null) {
            isFirstTimeVisible = savedInstanceState.getBoolean("isFirstTimeVisible", true);
            isInitialized = savedInstanceState.getBoolean("isInitialized", false);

            restoreState(savedInstanceState);
        }
    }

    // ==================== 可见性管理 ====================

    /**
     * 获取ViewBinding实例
     * 子类必须实现此方法来提供具体的ViewBinding
     * 
     * @param inflater 布局填充器
     * @param container 父容器
     * @return ViewBinding实例
     */
    protected abstract VB getViewBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup container);

    /**
     * 初始化视图组件
     * 在ViewBinding创建后调用，用于初始化UI组件和设置监听器
     */
    protected abstract void initView();

    /**
     * Fragment可见时的刷新操作
     * 当Fragment对用户可见时调用，用于刷新数据或恢复操作
     */
    protected abstract void onVisibleRefresh();

    /**
     * Fragment不可见时的刷新操作
     * 当Fragment对用户不可见时调用，用于暂停操作或释放资源
     */
    protected abstract void onInVisibleRefresh();

    // ==================== 可选回调方法 - 子类可以重写 ====================

    /**
     * Fragment第一次可见时的回调
     * 子类可以重写此方法来处理第一次可见的特殊逻辑
     */
    protected void onFirstTimeVisible() {
        Log.d(TAG, "onFirstTimeVisible: " + getClass().getSimpleName());
        // 子类可以重写此方法
    }

    /**
     * Fragment恢复时的回调
     * 在onResume()之后调用，子类可以重写此方法来处理Fragment恢复时的逻辑
     * 例如：恢复播放状态、重新开始动画、恢复网络请求等
     */
    protected void onFragmentResume() {
        Log.d(TAG, "onFragmentResume: " + getClass().getSimpleName());
        // 子类可以重写此方法
    }

    /**
     * Fragment暂停时的回调
     * 在onPause()之后调用，子类可以重写此方法来处理Fragment暂停时的逻辑
     * 例如：暂停播放、停止动画、暂停网络请求等
     */
    protected void onFragmentPause() {
        Log.d(TAG, "onFragmentPause: " + getClass().getSimpleName());
        // 子类可以重写此方法
    }

    /**
     * 保存Fragment状态
     * 子类可以重写此方法来保存特定的状态信息
     *
     * @param outState 用于保存状态的Bundle
     */
    protected void saveInstanceState(@NonNull Bundle outState) {
        // 子类可以重写此方法来保存状态
        Log.d(TAG, "saveInstanceState: Default implementation for " + getClass().getSimpleName());
    }

    /**
     * 恢复Fragment状态
     * 子类可以重写此方法来恢复特定的状态信息
     *
     * @param savedInstanceState 包含保存状态的Bundle
     */
    protected void restoreState(@NonNull Bundle savedInstanceState) {
        // 子类可以重写此方法来恢复状态
        Log.d(TAG, "restoreState: Default implementation for " + getClass().getSimpleName());
    }

    // ==================== 工具方法 ====================

    /**
     * 安全地执行需要ViewBinding的操作
     * 只有在ViewBinding初始化后才会执行
     *
     * @param action 要执行的操作
     */
    protected final void runWithBinding(@NonNull Runnable action) {
        if (binding != null) {
            try {
                action.run();
            } catch (Exception e) {
                Log.e(TAG, "Error running binding action in: " + getClass().getSimpleName(), e);
            }
        } else {
            Log.w(TAG, "ViewBinding not initialized in: " + getClass().getSimpleName());
        }
    }

    /**
     * 检查ViewBinding是否已初始化
     *
     * @return true如果ViewBinding已初始化
     */
    protected final boolean isBindingInitialized() {
        return binding != null;
    }

    /**
     * 检查Fragment是否已完全初始化
     *
     * @return true如果Fragment已完全初始化
     */
    protected boolean isFullyInitialized() {
        return isInitialized && binding != null;
    }

    /**
     * 检查Fragment是否当前可见
     *
     * @return true如果Fragment当前可见
     */
    protected final boolean isCurrentlyVisible() {
        return isCurrentlyVisible;
    }

    /**
     * 获取Fragment的显示名称
     * 用于日志记录和调试
     *
     * @return Fragment显示名称
     */
    protected String getFragmentDisplayName() {
        return getClass().getSimpleName();
    }

}
