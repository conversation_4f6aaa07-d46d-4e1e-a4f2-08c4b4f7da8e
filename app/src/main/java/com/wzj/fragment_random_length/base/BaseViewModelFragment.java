package com.wzj.fragment_random_length.base;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.lifecycle.ViewModel;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewbinding.ViewBinding;

/**
 * BaseViewModelFragment - 支持ViewModel的Fragment基类
 *
 * 功能特性：
 * 1. 继承BaseFragment的所有功能
 * 2. 集成ViewModel支持
 * 3. 自动创建和管理ViewModel
 * 4. 提供ViewModel观察者设置
 *
 * @param <VB> ViewBinding类型
 * @param <VM> ViewModel类型
 */
public abstract class BaseViewModelFragment<VB extends ViewBinding, VM extends ViewModel> extends BaseFragment<VB> {

    private static final String TAG = "BaseViewModelFragment";

    // ==================== ViewModel组件 ====================

    /** ViewModel实例 */
    protected VM viewModel;

    // ==================== Fragment生命周期 ====================

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container,
                            @Nullable Bundle savedInstanceState) {
        Log.d(TAG, "onCreateView (ViewModel): " + getClass().getSimpleName());

        if (binding == null) {
            // 初始化ViewBinding
            binding = getViewBinding(inflater, container);

            if (binding != null) {
                // 创建ViewModel
                viewModel = createViewModel();

                // 初始化视图
                initView();

                // 观察ViewModel数据变化
                observeViewModel();

                Log.d(TAG, "View created successfully with ViewModel: " + getClass().getSimpleName());
            }
        }
        return binding != null ? binding.getRoot() : null;
    }

    @Override
    public void onDestroyView() {
        super.onDestroyView();
        // ViewBinding会在父类中清理，ViewModel会自动清理
        viewModel = null;
    }

    // ==================== ViewModel管理 ====================

    /**
     * 创建ViewModel实例
     */
    protected VM createViewModel() {
        Class<VM> viewModelClass = getViewModelClass();
        if (viewModelClass != null) {
            return new ViewModelProvider(this).get(viewModelClass);
        }
        return null;
    }

    /**
     * 获取ViewModel的Class
     * 子类必须实现此方法来指定ViewModel类型
     */
    protected abstract Class<VM> getViewModelClass();

    /**
     * 观察ViewModel数据变化
     * 子类可以重写此方法来观察LiveData
     */
    protected void observeViewModel() {
        // 子类可以重写此方法
        Log.d(TAG, "observeViewModel: " + getClass().getSimpleName());
    }

    // ==================== 工具方法 ====================

    /**
     * 安全地执行需要ViewModel的操作
     * 只有在ViewModel初始化后才会执行
     *
     * @param action 要执行的操作
     */
    protected final void runWithViewModel(Runnable action) {
        if (viewModel != null && action != null) {
            try {
                action.run();
            } catch (Exception e) {
                Log.e(TAG, "Error running ViewModel action in: " + getClass().getSimpleName(), e);
            }
        } else {
            Log.w(TAG, "ViewModel not initialized or action is null in: " + getClass().getSimpleName());
        }
    }

    // ==================== 状态保存和恢复 ====================

    @Override
    public void onSaveInstanceState(@NonNull Bundle outState) {
        super.onSaveInstanceState(outState);
        Log.d(TAG, "onSaveInstanceState: " + getClass().getSimpleName());

        // 调用子类的状态保存方法
        saveInstanceState(outState);
    }

    @Override
    public void onViewStateRestored(@Nullable Bundle savedInstanceState) {
        super.onViewStateRestored(savedInstanceState);
        Log.d(TAG, "onViewStateRestored: " + getClass().getSimpleName());

        // 如果有保存的状态，调用子类的状态恢复方法
        if (savedInstanceState != null) {
            restoreState(savedInstanceState);
        }
    }

    /**
     * 保存Fragment状态
     * 子类可以重写此方法来保存特定的状态信息
     *
     * @param outState 用于保存状态的Bundle
     */
    protected void saveInstanceState(@NonNull Bundle outState) {
        // 子类可以重写此方法来保存状态
        Log.d(TAG, "saveInstanceState: Default implementation for " + getClass().getSimpleName());
    }

    /**
     * 恢复Fragment状态
     * 子类可以重写此方法来恢复特定的状态信息
     *
     * @param savedInstanceState 包含保存状态的Bundle
     */
    protected void restoreState(@NonNull Bundle savedInstanceState) {
        // 子类可以重写此方法来恢复状态
        Log.d(TAG, "restoreState: Default implementation for " + getClass().getSimpleName());
    }
}
