package com.wzj.fragment_random_length.constants;

public class AppConstants {
    public static final String ARG_INDEX = "index";
    public static final int DEFAULT_FRAGMENT_COUNT = 6;

    /**
     * SharedPreferences相关常量
     */
    public static final class SharedPrefs {
        public static final String PREF_NAME = "data";
        public static final String KEY_PREFIX = "key";

        public static String getFragmentStateKey(int index) {
            return KEY_PREFIX + index;
        }
    }
}
