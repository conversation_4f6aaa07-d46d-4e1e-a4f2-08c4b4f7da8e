package com.wzj.fragment_random_length.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.google.android.material.tabs.TabLayout;

public class DataFragmentViewModel extends AndroidViewModel {
    private static final String TAG = "DataFragmentViewModel";

    // ==================== LiveData 数据 ====================
    /** tabLayout选中状态LiveData */
    private final MutableLiveData<String> contentLiveData = new MutableLiveData<>();
    public DataFragmentViewModel(@NonNull Application application) {
        super(application);
        initializeDefaultState();
    }

    // ==================== 私有方法 ====================
    //初始化默认状态
    private void initializeDefaultState() {
        Log.d(TAG, "initializeDefaultState");
        // 初始化应用状态

    }

    //获取当前位置LiveData
    public LiveData<String> getContentLiveData() {
        return contentLiveData;
    }

    public void setContentLiveData(String content) {
        contentLiveData.setValue(content);
    }

}
