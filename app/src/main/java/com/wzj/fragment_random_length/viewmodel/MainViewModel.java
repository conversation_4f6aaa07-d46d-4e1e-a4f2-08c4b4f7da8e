package com.wzj.fragment_random_length.viewmodel;

import android.app.Application;
import android.util.Log;

import androidx.annotation.NonNull;
import androidx.lifecycle.AndroidViewModel;
import androidx.lifecycle.LiveData;
import androidx.lifecycle.MutableLiveData;

import com.google.android.material.tabs.TabLayout;

public class MainViewModel extends AndroidViewModel {
    private static final String TAG = "MainViewModel";

    // ==================== LiveData 数据 ====================
    /** tabLayout选中状态LiveData */
    private final MutableLiveData<TabLayout.Tab> tabSelectedLiveData = new MutableLiveData<>();
    private final MutableLiveData<TabLayout.Tab> tabUnSelectedLiveData = new MutableLiveData<>();

    public MainViewModel(@NonNull Application application) {
        super(application);
        Log.d(TAG, "MainViewModel created");
        initializeDefaultState();
    }

    // ==================== 私有方法 ====================
     //初始化默认状态
    private void initializeDefaultState() {
        Log.d(TAG, "initializeDefaultState");
        // 初始化应用状态

    }

    //获取当前位置LiveData
    public LiveData<TabLayout.Tab> getTabSelectedLiveData() {
        return tabSelectedLiveData;
    }

    //获取当前位置LiveData
    public LiveData<TabLayout.Tab> getTabUnSelectedLiveData() {
        return tabUnSelectedLiveData;
    }

    //选中
    public void setTabSelectedLiveData(TabLayout.Tab tab) {
        TabLayout.Tab tab1 = tabSelectedLiveData.getValue();
        if (tab1 == null || tab != tab1) {
            tabSelectedLiveData.setValue(tab);
        } else {
            Log.d(TAG, "setTabSelectedLiveData is already current, skipping update");
        }
    }

    //选中
    public void setTabUnSelectedLiveData(TabLayout.Tab tab) {
        TabLayout.Tab tab1 = tabUnSelectedLiveData.getValue();
        if (tab1 == null || tab != tab1) {
            tabUnSelectedLiveData.setValue(tab);
        } else {
            Log.d(TAG, "setTabUnSelectedLiveData is already current, skipping update");
        }
    }
}
