package com.wzj.fragment_random_length.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.wzj.fragment_random_length.base.BaseViewModelFragment;
import com.wzj.fragment_random_length.constants.AppConstants;
import com.wzj.fragment_random_length.databinding.FragmentDataBinding;
import com.wzj.fragment_random_length.viewmodel.DataFragmentViewModel;

public class DataFragment extends BaseViewModelFragment<FragmentDataBinding, DataFragmentViewModel> {
    private final String TAG = "DataFragment";

    public static DataFragment newInstance(int index) {
        DataFragment fragment = new DataFragment();
        Bundle args = new Bundle();
        args.putInt(AppConstants.ARG_INDEX, index);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected Class<DataFragmentViewModel> getViewModelClass() {
        return DataFragmentViewModel.class;
    }

    @Override
    protected FragmentDataBinding getViewBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup container) {
        return FragmentDataBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        Log.e(TAG, "initView: " );
    }

    @Override
    protected void observeViewModel() {
        super.observeViewModel();
        if (viewModel != null) {

        }
    }

    @Override
    protected void onFirstTimeVisible() {
        super.onFirstTimeVisible();
        Log.e(TAG, "onFirstTimeVisible: " );
    }

    @Override
    protected void onVisibleRefresh() {
        Log.e(TAG, "onVisibleRefresh: " );
    }

    @Override
    protected void onInVisibleRefresh() {
        Log.e(TAG, "onInVisibleRefresh: " );
    }


}
