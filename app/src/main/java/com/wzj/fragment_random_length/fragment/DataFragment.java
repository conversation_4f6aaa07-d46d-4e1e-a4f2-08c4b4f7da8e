package com.wzj.fragment_random_length.fragment;

import android.os.Bundle;
import android.util.Log;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.wzj.fragment_random_length.R;
import com.wzj.fragment_random_length.base.BaseViewModelFragment;
import com.wzj.fragment_random_length.constants.AppConstants;
import com.wzj.fragment_random_length.databinding.FragmentDataBinding;
import com.wzj.fragment_random_length.utils.SharedPreferencesUtil;
import com.wzj.fragment_random_length.viewmodel.DataFragmentViewModel;

public class DataFragment extends BaseViewModelFragment<FragmentDataBinding, DataFragmentViewModel> implements View.OnClickListener {
    private final String TAG = "DataFragment";
    private int index = -1;

    public static DataFragment newInstance(int index) {
        DataFragment fragment = new DataFragment();
        Bundle args = new Bundle();
        args.putInt(AppConstants.ARG_INDEX, index);
        fragment.setArguments(args);
        return fragment;
    }

    @Override
    protected Class<DataFragmentViewModel> getViewModelClass() {
        return DataFragmentViewModel.class;
    }

    @Override
    protected FragmentDataBinding getViewBinding(@NonNull LayoutInflater inflater, @Nullable ViewGroup container) {
        return FragmentDataBinding.inflate(getLayoutInflater());
    }

    @Override
    protected void initView() {
        Log.e(TAG, "initView: " );

        // 安全地获取传递的参数
        Bundle arguments = getArguments();
        if (arguments != null) {
            index = arguments.getInt(AppConstants.ARG_INDEX, 0);
            Log.e(TAG, "initView: "+index );
        } else {
            Log.w(TAG, "No arguments provided, using default index 0");
            index = 0;
        }
        binding.textView.setOnClickListener(this);

        try {
            String stateKey = AppConstants.SharedPrefs.getFragmentStateKey(index);
            boolean isActive = (Boolean) SharedPreferencesUtil.getData(stateKey, false);

            String content = isActive
                    ? "我是第"+(index + 1)+"个Fragment,我的下标是"+index+"！"
                    : "当前是第"+(index + 1)+"个DataFragment!";
            viewModel.setContentLiveData(content);
            Log.d(TAG, "Content updated for index " + index + ", isActive: " + isActive);
        } catch (Exception e) {
            Log.e(TAG, "Error updating fragment content: " + e.getMessage(), e);
            // 设置默认内容
            viewModel.setContentLiveData("Fragment " + (index + 1));

        }

    }

    @Override
    protected void observeViewModel() {
        super.observeViewModel();
        if (viewModel != null) {
            viewModel.getContentLiveData().observe(this, content -> {
                binding.textView.setText(content);
            });
        }
    }

    @Override
    protected void onFirstTimeVisible() {
        super.onFirstTimeVisible();
        Log.e(TAG, "onFirstTimeVisible: " );
    }

    @Override
    protected void onVisibleRefresh() {
        Log.e(TAG, "onVisibleRefresh: " );
    }

    @Override
    protected void onInVisibleRefresh() {
        Log.e(TAG, "onInVisibleRefresh: " );
    }


    @Override
    public void onClick(View v) {
        if(v.getId() == R.id.textView){
            try {
                String key = AppConstants.SharedPrefs.getFragmentStateKey(index);
                boolean currentState = (Boolean) SharedPreferencesUtil.getData(key, false);
                boolean newState = !currentState;
                SharedPreferencesUtil.putData(key, newState);
                String content = newState
                        ? "我是第"+(index + 1)+"个Fragment,我的下标是"+index+"！"
                        : "当前是第"+(index + 1)+"个DataFragment!";
                viewModel.setContentLiveData(content);
            }catch (Exception e){
                Log.e(TAG, "Error handling click: " + e.getMessage(), e);
            }
        }
    }
}
