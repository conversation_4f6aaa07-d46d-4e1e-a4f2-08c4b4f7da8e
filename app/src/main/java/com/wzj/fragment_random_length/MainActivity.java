package com.wzj.fragment_random_length;

import android.os.Build;
import android.os.Bundle;
import android.util.Log;
import android.view.View;
import android.widget.LinearLayout;

import androidx.appcompat.app.AppCompatActivity;
import androidx.lifecycle.ViewModelProvider;
import androidx.viewpager.widget.ViewPager;

import com.google.android.material.tabs.TabLayout;
import com.wzj.fragment_random_length.constants.AppConstants;
import com.wzj.fragment_random_length.databinding.ActivityMainBinding;
import com.wzj.fragment_random_length.utils.MyPagerAdapter;
import com.wzj.fragment_random_length.utils.SharedPreferencesUtil;
import com.wzj.fragment_random_length.viewmodel.MainViewModel;

import java.util.ArrayList;
import java.util.List;

public class MainActivity extends AppCompatActivity {
    private final String TAG = "MainActivity";
    //ViewBinding实例
    private ActivityMainBinding binding;
    //MainViewModel实例
    private MainViewModel viewModel;

//    private ViewPager viewPager;
    private MyPagerAdapter pagerAdapter;
//    private TabLayout tabLayout;
    private List<Integer> fragmentIndexes;

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        // 初始化ViewBinding
        binding = ActivityMainBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());
        try {
            SharedPreferencesUtil.getInstance(this, AppConstants.SharedPrefs.PREF_NAME);
        } catch (Exception e) {
            Log.e(TAG, "Failed to initialize SharedPreferences: " + e.getMessage(), e);
        }
        initData();
        // 初始化ViewModel
        viewModel = new ViewModelProvider(this).get(MainViewModel.class);
        // 我们将手动管理UI更新
        setupViewModelObservers();

        setupViewPager();
        setupTabLayout();

    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.e(TAG, "onResume: " );
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 清理资源
        if (pagerAdapter != null) {
            pagerAdapter.clearFragments();
        }
    }

    private void setupViewPager() {
        Log.e(TAG, "setupViewPager: " );
        try {
            pagerAdapter = new MyPagerAdapter(getSupportFragmentManager());
            pagerAdapter.setFragmentIndexes(fragmentIndexes);
            binding.viewPager.setAdapter(pagerAdapter);
        } catch (Exception e) {
            Log.e(TAG, "Failed to setup ViewPager: " + e.getMessage(), e);
        }
    }

    /**
     * 设置TabLayout
     */
    private void setupTabLayout() {
        Log.e(TAG, "setupTabLayout: " );
        try {
            // 关联 TabLayout 和 ViewPager
            binding.tabLayout.setupWithViewPager(binding.viewPager);
            binding.tabLayout.addOnTabSelectedListener(new TabLayout.OnTabSelectedListener() {
                @Override
                public void onTabSelected(TabLayout.Tab tab) {
                    // 可以在这里添加tab选中的逻辑
                    Log.d(TAG, "Tab selected: " + tab.getPosition());
                    viewModel.setTabSelectedLiveData(tab);
                }

                @Override
                public void onTabUnselected(TabLayout.Tab tab) {
                    // 可以在这里添加tab取消选中的逻辑
                    viewModel.setTabUnSelectedLiveData(tab);

                }

                @Override
                public void onTabReselected(TabLayout.Tab tab) {
                    // 可以在这里添加tab重新选中的逻辑
                }
            });
            binding.tabLayout.selectTab(binding.tabLayout.getTabAt(0));

            // 手动触发第一个tab的选中状态，因为selectTab不会触发onTabSelected回调
            TabLayout.Tab firstTab = binding.tabLayout.getTabAt(0);
            if (firstTab != null) {
                viewModel.setTabSelectedLiveData(firstTab);
            }
        } catch (Exception e) {
            Log.e(TAG, "Failed to setup TabLayout: " + e.getMessage(), e);
        }
    }

    private void initData(){
        Log.e(TAG, "initData: " );
        fragmentIndexes = new ArrayList<>();
        for (int i = 0; i < AppConstants.DEFAULT_FRAGMENT_COUNT; i++) {
            fragmentIndexes.add(i);
            try {
                SharedPreferencesUtil.putData(AppConstants.SharedPrefs.getFragmentStateKey(i), false);
            } catch (Exception e) {
                Log.w(TAG, "Failed to save data for " + AppConstants.SharedPrefs.getFragmentStateKey(i) + ": " + e.getMessage());
            }
        }
    }

    private void setupViewModelObservers() {
        Log.e(TAG, "setupViewModelObservers: " );
        if (viewModel != null) {
            // 观察应用状态变化
            viewModel.getTabSelectedLiveData().observe(this, tab -> {
                Log.e(TAG, "setupViewModelObservers: "+tab );
                View customView = tab.getCustomView();
                if (customView != null) {
                    // 设置选中状态背景
                    customView.setBackgroundResource(R.color.white);
                } else {
                    // 如果没有自定义视图，直接设置 Tab 背景（需要 API 21+）
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        tab.view.setBackgroundResource(R.color.white);
                    }
                }
                Log.d(TAG, "getTabSelectedLiveData observers setup completed");
            });

            // 观察应用状态变化
            viewModel.getTabUnSelectedLiveData().observe(this, tab -> {
                View customView = tab.getCustomView();
                if (customView != null) {
                    // 设置选中状态背景
                    customView.setBackgroundResource(R.color.transparent);
                } else {
                    // 如果没有自定义视图，直接设置 Tab 背景（需要 API 21+）
                    if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                        tab.view.setBackgroundResource(R.color.transparent);
                    }
                }
                Log.d(TAG, "getTabUnSelectedLiveData observers setup completed");
            });
        }
    }
}